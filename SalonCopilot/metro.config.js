const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// Add resolver for Node.js modules
config.resolver.alias = {
  ...config.resolver.alias,
  crypto: 'crypto-browserify',
  stream: require.resolve('./stream-mock.js'),
  buffer: 'buffer',
  events: 'events',
  util: 'util',
  // Mock ws module for React Native
  ws: require.resolve('./ws-mock.js'),
};

// Add node modules to resolver
config.resolver.nodeModulesPaths = [
  ...config.resolver.nodeModulesPaths,
  './node_modules',
];

module.exports = config;
