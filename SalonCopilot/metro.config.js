const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// Add resolver for Node.js modules
config.resolver.alias = {
  ...config.resolver.alias,
  crypto: 'react-native-polyfill-globals/src/crypto',
  stream: 'react-native-polyfill-globals/src/readable-stream',
  buffer: 'react-native-polyfill-globals/src/buffer',
  events: 'react-native-polyfill-globals/src/events',
  util: 'react-native-polyfill-globals/src/util',
  'web-streams-polyfill/ponyfill/es6': 'web-streams-polyfill/ponyfill/es6',
};

// Add node modules to resolver
config.resolver.nodeModulesPaths = [
  ...config.resolver.nodeModulesPaths,
  './node_modules',
];

module.exports = config;
