{"name": "saloncopilot", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@supabase/supabase-js": "^2.49.8", "expo": "~53.0.9", "expo-constants": "^17.1.6", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-url-polyfill": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}