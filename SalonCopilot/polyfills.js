// Polyfills for React Native
import 'react-native-polyfill-globals/auto';

// Web Streams polyfill
import { ReadableStream, WritableStream, TransformStream } from 'web-streams-polyfill/ponyfill/es6';

// Add to global scope
if (typeof global !== 'undefined') {
  global.ReadableStream = ReadableStream;
  global.WritableStream = WritableStream;
  global.TransformStream = TransformStream;
}

// For web environments
if (typeof window !== 'undefined') {
  window.ReadableStream = ReadableStream;
  window.WritableStream = WritableStream;
  window.TransformStream = TransformStream;
}
